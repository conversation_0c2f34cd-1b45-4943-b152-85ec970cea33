package gowatch

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	// Test constants
	DefaultTestChannelSize = 1                      // Default size for test channels
	DefaultTestTimeout     = 1 * time.Second        // Default timeout for test operations
	DefaultTestSleepTime   = 100 * time.Millisecond // Default sleep time for tests
	DefaultTestDocCount    = 3                      // Default number of test documents
	DefaultTestWaitTime    = 2 * time.Second        // Default wait time for tests
)

// setupTest sets up the test environment
func setupTestWatch(t *testing.T) func(*gomongo.MongoCollection) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	gohelper.SetRmbaseFileCfg(configPath)
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	// Return cleanup function
	return func(coll *gomongo.MongoCollection) {
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup collection: %v", err)
		}
	}
}

func NewMockMongoCollection(name string) *gomongo.MongoCollection {
	// Try to get the collection from rni.sysdata first
	col := gomongo.Coll("tmp", name)

	if col == nil {
		golog.Error("Failed to create test collection.", "name", name)
	}
	return col
}

func TestWatchTarget(t *testing.T) {
	cleanup := setupTestWatch(t)

	// Create test collection
	col := NewMockMongoCollection("import_watched_records")
	defer cleanup(col)
	if col == nil {
		t.Fatal("Failed to create test collection")
	}

	// Clean up any existing test documents
	_, err := col.DeleteMany(context.Background(), bson.M{})
	if err != nil {
		t.Logf("Warning: Failed to clean up test collection: %v", err)
	}

	// Channel to track watch events
	watchEvents := make(chan bool, DefaultTestChannelSize)
	eventCount := 0
	ctx, cancel := context.WithCancel(context.Background())

	tests := []struct {
		name    string
		opt     WatchOptions
		wantErr bool
	}{
		{
			name: "valid options",
			opt: WatchOptions{
				WatchedColl: col,
				OnError: func(err error) {
					t.Logf("Watch error: %v", err)
				},
				OnChange: func(doc bson.M, coll *gomongo.MongoCollection) error {
					eventCount++
					if eventCount == DefaultTestChannelSize {
						watchEvents <- true
					}
					return nil
				},
				OnTokenUpdate: func(opt TokenUpdateOptions) error {
					t.Logf("Token updated: %v", opt)
					return nil
				},
				QueryWhenInvalidToken: bson.M{},
				HighWaterMark:         DefaultHighWaterMark,
				WaterMarkerTimerS:     DefaultTestTimeout.Seconds(), // Reduce timer interval for testing
				UpdateTokenTimerS:     DefaultTestTimeout.Seconds(), // Reduce timer interval for testing
				OnClose: func() {
					t.Log("Watch closed")
				},
				Context: ctx,
				Cancel:  cancel,
			},
			wantErr: false,
		},
		{
			name: "missing watched collection",
			opt: WatchOptions{
				WatchedColl: nil,
				OnError: func(err error) {
					t.Logf("Watch error: %v", err)
				},
				OnChange: func(doc bson.M, coll *gomongo.MongoCollection) error {
					return nil
				},
				Context: ctx,
				Cancel:  cancel,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Skip nil collection test if we don't have a valid collection
			if tt.opt.WatchedColl == nil && col == nil {
				t.Skip("Skipping nil collection test because no valid collection is available")
				return
			}

			// Create a done channel to signal WatchTarget completion
			done := make(chan struct{})
			watchObjCh := make(chan *WatchObject)
			var watchObj *WatchObject
			var watchErr error

			// Start WatchTarget in a separate goroutine
			go func() {
				watchObj, watchErr = WatchTarget(tt.opt)
				if watchErr != nil {
					t.Logf("WatchTarget error: %v", watchErr)
				}
				watchObjCh <- watchObj
				close(done)
			}()

			select {
			case watchObj = <-watchObjCh:
				if watchObj == nil {
					t.Log("watchObj is nil, skipping test")
					return
				}
			case <-time.After(DefaultTestTimeout):
				t.Error("Timeout waiting for watchObj")
				return
			}

			// Wait a bit for the watch to start
			time.Sleep(DefaultTestSleepTime)

			docID := "test" + time.Now().Format("20060102150405")
			_, err := col.InsertOne(context.Background(), bson.M{"_id": docID, "value": "test"})
			if err != nil {
				t.Errorf("Failed to insert test document: %v", err)
			}

			// Update test document
			_, err = col.UpdateOne(
				context.Background(),
				bson.M{"_id": docID},
				bson.M{"$set": bson.M{"value": "updated"}},
			)
			if err != nil {
				t.Errorf("Failed to update test document: %v", err)
			}

			// Replace test document
			_, err = col.ReplaceOne(
				context.Background(),
				bson.M{"_id": docID},
				bson.M{"_id": docID, "value": "replaced", "newField": "newValue"},
			)
			if err != nil {
				t.Errorf("Failed to replace test document: %v", err)
			}

			// Delete test document
			t.Log("Deleting test document")
			_, err = col.DeleteOne(context.Background(), bson.M{"_id": docID})
			if err != nil {
				t.Errorf("Failed to delete test document: %v", err)
			}

			if watchObj != nil {
				t.Log("Ending watch...")
				watchObj.End(nil)
			} else {
				t.Error("watchObj is nil after WatchTarget")
			}

			// Wait for either watch event or timeout
			t.Log("Waiting for watch event...")
			select {
			case <-watchEvents:
				t.Log("Received watch event")
			case <-time.After(DefaultTestTimeout):
				t.Log("Timeout waiting for watch event")
			}

			// Wait for WatchTarget to complete or timeout
			t.Log("Waiting for WatchTarget to complete...")
			select {
			case <-done:
				t.Log("WatchTarget completed")
				if (watchErr != nil) != tt.wantErr {
					t.Errorf("WatchTarget() error = %v, wantErr %v", watchErr, tt.wantErr)
					return
				}
				if !tt.wantErr && watchObj == nil {
					t.Fatal("WatchTarget returned nil watch object")
				}
			case <-time.After(DefaultTestWaitTime):
				if !tt.wantErr {
					t.Error("Timeout waiting for WatchTarget to return")
					return
				}
			}

			// If we got a watch object, make sure to clean it up
			if watchObj != nil {
				t.Log("Watch object created successfully")

				// Clean up
				t.Log("Cleaning up watch object")
				watchObj.cancel()
			}
		})
	}
}

func TestProcessChangedObject(t *testing.T) {
	cleanup := setupTestWatch(t)

	// Create test collection
	col := NewMockMongoCollection("test_process")
	defer cleanup(col)

	tests := []struct {
		name    string
		opt     ProcessChangedObjectOptions
		wantErr bool
	}{
		{
			name: "valid insert operation",
			opt: ProcessChangedObjectOptions{
				ChangedObj: bson.M{
					"operationType": "insert",
					"fullDocument": bson.M{
						"_id": "test-insert-id",
					},
					"documentKey": bson.M{
						"_id": "test-insert-id",
					},
				},
				WatchedColl: col,
				InsertOneFn: func(bson.M) error { return nil },
			},
			wantErr: false,
		},
		{
			name: "valid update operation",
			opt: ProcessChangedObjectOptions{
				ChangedObj: bson.M{
					"operationType": "update",
					"documentKey": bson.M{
						"_id": "test-update-id",
					},
					"updateDescription": bson.M{
						"updatedFields": bson.M{"status": "updated"},
					},
					"fullDocument": bson.M{
						"_id":    "test-update-id",
						"status": "updated",
					},
				},
				WatchedColl: col,
				UpdateOneFn: func(bson.M) error { return nil },
			},
			wantErr: false,
		},
		{
			name: "valid replace operation",
			opt: ProcessChangedObjectOptions{
				ChangedObj: bson.M{
					"operationType": "replace",
					"documentKey": bson.M{
						"_id": "test-replace-id",
					},
					"fullDocument": bson.M{
						"_id":   "test-replace-id",
						"value": "replaced",
					},
				},
				WatchedColl:  col,
				ReplaceOneFn: func(bson.M) error { return nil },
			},
			wantErr: false,
		},
		{
			name: "valid delete operation",
			opt: ProcessChangedObjectOptions{
				ChangedObj: bson.M{
					"operationType": "delete",
					"documentKey": bson.M{
						"_id": "test-delete-id",
					},
				},
				WatchedColl: col,
				DeleteOneFn: func(interface{}) error { return nil },
			},
			wantErr: false,
		},
		{
			name: "invalid operation type",
			opt: ProcessChangedObjectOptions{
				ChangedObj: bson.M{
					"operationType": "invalid",
				},
				WatchedColl: col,
			},
			wantErr: false, // Should not return error for invalid operation type
		},
		{
			name: "update with missing updateDescription",
			opt: ProcessChangedObjectOptions{
				ChangedObj: bson.M{
					"operationType": "update",
					"documentKey": bson.M{
						"_id": "test-id",
					},
					"fullDocument": bson.M{
						"_id":    "test-id",
						"status": "updated",
					},
				},
				WatchedColl: col,
				UpdateOneFn: func(bson.M) error { return nil },
			},
			wantErr: false,
		},
		{
			name: "delete with ignoreDelete true",
			opt: ProcessChangedObjectOptions{
				ChangedObj: bson.M{
					"operationType": "delete",
					"documentKey": bson.M{
						"_id": "test-id",
					},
				},
				WatchedColl:  col,
				DeleteOneFn:  func(interface{}) error { return nil },
				IgnoreDelete: true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ProcessChangedObject(tt.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessChangedObject() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGetToken(t *testing.T) {
	cleanup := setupTestWatch(t)

	// Create test collection
	col := NewMockMongoCollection("test_token")
	defer cleanup(col)

	tests := []struct {
		name    string
		col     *gomongo.MongoCollection
		setup   func() error
		wantErr bool
	}{
		{
			name:    "valid collection",
			col:     col,
			setup:   func() error { return nil },
			wantErr: false,
		},
		{
			name:    "nil collection",
			col:     nil,
			setup:   func() error { return nil },
			wantErr: true,
		},
		{
			name: "collection with document change",
			col:  col,
			setup: func() error {
				// Insert a document to trigger change
				_, err := col.InsertOne(context.Background(), bson.M{"test": "value"})
				return err
			},
			wantErr: false,
		},
		{
			name: "collection with multiple changes",
			col:  col,
			setup: func() error {
				// Insert multiple documents to trigger changes
				docs := []interface{}{
					bson.M{"test": "value1"},
					bson.M{"test": "value2"},
					bson.M{"test": "value3"},
				}
				_, err := col.InsertMany(context.Background(), docs)
				return err
			},
			wantErr: false,
		},
		{
			name: "collection with update operation",
			col:  col,
			setup: func() error {
				// Insert and then update a document
				doc := bson.M{"_id": "test-update-id", "value": "initial"}
				if _, err := col.InsertOne(context.Background(), doc); err != nil {
					return err
				}
				_, err := col.UpdateOne(
					context.Background(),
					bson.M{"_id": "test-update-id"},
					bson.M{"$set": bson.M{"value": "updated"}},
				)
				return err
			},
			wantErr: false,
		},
		{
			name: "collection with delete operation",
			col:  col,
			setup: func() error {
				// Insert and then delete a document
				doc := bson.M{"_id": "test-delete-id", "value": "to-delete"}
				if _, err := col.InsertOne(context.Background(), doc); err != nil {
					return err
				}
				_, err := col.DeleteOne(context.Background(), bson.M{"_id": "test-delete-id"})
				return err
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Skip nil collection test if we don't have a valid collection
			if tt.col == nil && col == nil {
				t.Skip("Skipping nil collection test because no valid collection is available")
				return
			}

			// Run setup if provided
			if tt.setup != nil {
				if err := tt.setup(); err != nil {
					t.Fatalf("Setup failed: %v", err)
				}
			}

			// Test database connection
			if tt.col != nil {
				if err := gomongo.InitMongoDB(); err != nil {
					t.Fatalf("Failed to initialize MongoDB: %v", err)
				}
				// Wait a bit for connection pool to initialize
				time.Sleep(DefaultTestWaitTime)
				if err := tt.col.Ping(context.Background()); err != nil {
					t.Fatalf("Failed to ping database: %v", err)
				}
			}

			// Get token and verify result
			token, tokenClusterTs, err := GetToken(tt.col)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Verify token and timestamp for successful cases
			if !tt.wantErr {
				if token == nil {
					t.Error("GetToken() returned nil token")
					return
				}
				if tokenClusterTs.IsZero() {
					t.Error("GetToken() returned zero timestamp")
					return
				}
				if tokenData, ok := (*token)["_data"]; !ok || tokenData == nil {
					t.Error("GetToken() returned token with nil or missing _data field")
				}
			}
		})
	}
}
