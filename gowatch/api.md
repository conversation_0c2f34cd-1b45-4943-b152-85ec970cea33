<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gowatch

```go
import "github.com/real-rm/gowatch"
```

## Index

- [Constants](<#constants>)
- [func GetNewTokenAndProcessRemainingDocs\(opt GetNewTokenAndProcessRemainingDocsOptions\) \(\*bson.M, time.Time, error\)](<#GetNewTokenAndProcessRemainingDocs>)
- [func GetToken\(col \*gomongo.MongoCollection\) \(\*bson.M, time.Time, error\)](<#GetToken>)
- [func ProcessChangedObject\(opt ProcessChangedObjectOptions\) error](<#ProcessChangedObject>)
- [type GetNewTokenAndProcessRemainingDocsOptions](<#GetNewTokenAndProcessRemainingDocsOptions>)
- [type ProcessChangedObjectOptions](<#ProcessChangedObjectOptions>)
- [type ProcessUpdateOptions](<#ProcessUpdateOptions>)
- [type TokenUpdateOptions](<#TokenUpdateOptions>)
- [type UpdateFields](<#UpdateFields>)
- [type UpdateSysDataFunc](<#UpdateSysDataFunc>)
  - [func GetUpdateSysdataFunction\(SysData \*gomongo.MongoCollection, SysDataId interface\{\}\) UpdateSysDataFunc](<#GetUpdateSysdataFunction>)
- [type WatchObject](<#WatchObject>)
  - [func WatchTarget\(opt WatchOptions\) \(\*WatchObject, error\)](<#WatchTarget>)
  - [func \(w \*WatchObject\) End\(err error\)](<#WatchObject.End>)
  - [func \(w \*WatchObject\) FinishAndUpdateSysdata\(updateSysData UpdateSysDataFunc\) error](<#WatchObject.FinishAndUpdateSysdata>)
  - [func \(w \*WatchObject\) Pause\(\)](<#WatchObject.Pause>)
  - [func \(w \*WatchObject\) ProcessChanges\(opt WatchOptions\)](<#WatchObject.ProcessChanges>)
  - [func \(w \*WatchObject\) Resume\(\)](<#WatchObject.Resume>)
- [type WatchOptions](<#WatchOptions>)
- [type WatchState](<#WatchState>)


## Constants

<a name="ChangeStreamHistoryLost"></a>

```go
const (
    // Error codes for MongoDB change stream operations
    ChangeStreamHistoryLost = 286 // Error when change stream history is lost
    InvalidToken            = 280 // Error when token is invalid
    CappedPositionLost      = 136 // Error when capped collection position is lost

    // Default configuration values
    DefaultHighWaterMark     = 10  // Maximum number of documents to process before pausing
    DefaultWaterMarkerTimerS = 600 // 10 minutes timer for water mark checks
    DefaultUpdateTokenTimerS = 60  // 1 minute timer for token updates
    DefaultChangeChanSize    = 100 // Default size for change channel buffer
    DefaultErrorChanSize     = 10  // Default size for error channel buffer

    // Time constants
    DefaultMaxAwaitTime           = 3 * time.Hour          // Maximum await time for change stream
    DefaultSleepInterval          = 100 * time.Millisecond // Default sleep interval for loops
    DefaultHighWaterMarkThreshold = 1                      // Threshold for high water mark warning
    DefaultStateRunning           = 0                      // State value for running
    DefaultStatePaused            = 1                      // State value for paused
    DefaultStateStopped           = 2                      // State value for stopped

    // Atomic operation constants
    DefaultDecrementValue = -1 // Default value for decrementing counters

    // Increment constants
    DefaultIncrementValue = 1 // Default value for incrementing counters

    // Unix timestamp constants
    DefaultUnixTimestamp = 0 // Default Unix timestamp value
)
```

<a name="DefaultTokenTimeout"></a>

```go
const (
    // Time constants
    DefaultTokenTimeout  = 60 * time.Second // Default timeout for token operations
    DefaultMaxFindTime   = 3 * time.Hour    // Maximum time for find operations
    DefaultUpdateTimeout = 5 * time.Second  // Default timeout for update operations
    DefaultCheckInterval = 1 * time.Second  // Default interval for checking completion
    DefaultMaxCheckLoops = 10               // Maximum number of completion check loops

    // Binary constants
    DefaultBinarySubType = "00" // Default binary subtype for tokens

    // High water mark constants
    DefaultMinHighWaterMark = 1 // Minimum high water mark value
    DefaultVerboseLevel     = 3 // Default verbose level for streaming
)
```

<a name="DefaultDuplicateCheckInterval"></a>

```go
const DefaultDuplicateCheckInterval = 2 * time.Second // Interval for duplicate update check
```

<a name="GetNewTokenAndProcessRemainingDocs"></a>
## func [GetNewTokenAndProcessRemainingDocs](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L115>)

```go
func GetNewTokenAndProcessRemainingDocs(opt GetNewTokenAndProcessRemainingDocsOptions) (*bson.M, time.Time, error)
```

GetNewTokenAndProcessRemainingDocs gets a new token and processes remaining documents

<a name="GetToken"></a>
## func [GetToken](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L44>)

```go
func GetToken(col *gomongo.MongoCollection) (*bson.M, time.Time, error)
```

GetToken gets a new token from a collection

<a name="ProcessChangedObject"></a>
## func [ProcessChangedObject](<https://github.com/real-rm/gowatch/blob/main/watch_processor.go#L33>)

```go
func ProcessChangedObject(opt ProcessChangedObjectOptions) error
```

ProcessChangedObject processes a changed object from the change stream

<a name="GetNewTokenAndProcessRemainingDocsOptions"></a>
## type [GetNewTokenAndProcessRemainingDocsOptions](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L105-L112>)

GetNewTokenAndProcessRemainingDocsOptions contains options for getting new token and processing remaining docs

```go
type GetNewTokenAndProcessRemainingDocsOptions struct {
    FromCol       *gomongo.MongoCollection // Collection to process
    Query         bson.M                   // Query to find remaining documents
    ProcessOneFn  func(bson.M) error       // Function to process each document
    HighWaterMark int                      // Maximum number of documents to process at once
    Context       context.Context          // Context for the operation
    Cancel        context.CancelFunc       // Cancel function for the operation
}
```

<a name="ProcessChangedObjectOptions"></a>
## type [ProcessChangedObjectOptions](<https://github.com/real-rm/gowatch/blob/main/watch_processor.go#L18-L30>)

ProcessChangedObjectOptions contains options for processing changed objects

```go
type ProcessChangedObjectOptions struct {
    ChangedObj            bson.M
    WatchedColl           *gomongo.MongoCollection
    DeleteOneFn           func(interface{}) error
    IgnoreDelete          bool
    UpdateOneFn           func(bson.M) error
    UpdateOneFnWithFields func(bson.M, bson.M) error
    ReplaceOneFn          func(bson.M) error
    InsertOneFn           func(bson.M) error
    ChangeStatusOnlyFn    func(bson.M) error
    SkipChangeMtOnly      bool
    WatchedStream         *WatchObject
}
```

<a name="ProcessUpdateOptions"></a>
## type [ProcessUpdateOptions](<https://github.com/real-rm/gowatch/blob/main/watch_processor.go#L245-L253>)

ProcessUpdateOptions contains options for processing updates

```go
type ProcessUpdateOptions struct {
    UpdateOneFn           func(bson.M) error
    UpdateOneFnWithFields func(bson.M, bson.M) error
    InsertOneFn           func(bson.M) error
    ReplaceOneFn          func(bson.M) error
    OperationType         string
    FullDocument          bson.M
    UpdatedFields         bson.M
}
```

<a name="TokenUpdateOptions"></a>
## type [TokenUpdateOptions](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L35-L41>)

TokenUpdateOptions contains token update information

```go
type TokenUpdateOptions struct {
    Token          *bson.M     // The new token
    TokenClusterTs time.Time   // Timestamp when token was created
    TokenChangeTs  time.Time   // Timestamp when token was updated
    End            func(error) // Function to end the watch operation
    ResumeMt       time.Time   // Timestamp to resume from
}
```

<a name="UpdateFields"></a>
## type [UpdateFields](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L204-L210>)

UpdateFields contains the fields to update in the system data

```go
type UpdateFields struct {
    Token          *bson.M
    TokenClusterTs time.Time
    TokenChangeTs  time.Time
    Status         string
    ResumeMt       time.Time
}
```

<a name="UpdateSysDataFunc"></a>
## type [UpdateSysDataFunc](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L213>)

UpdateSysDataFunc is the function type for updating system data

```go
type UpdateSysDataFunc func(UpdateFields) error
```

<a name="GetUpdateSysdataFunction"></a>
### func [GetUpdateSysdataFunction](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L216-L219>)

```go
func GetUpdateSysdataFunction(SysData *gomongo.MongoCollection, SysDataId interface{}) UpdateSysDataFunc
```

GetUpdateSysdataFunction returns a function to update system data

<a name="WatchObject"></a>
## type [WatchObject](<https://github.com/real-rm/gowatch/blob/main/watch.go#L79-L106>)

WatchObject represents the watch operation state

```go
type WatchObject struct {
    // contains filtered or unexported fields
}
```

<a name="WatchTarget"></a>
### func [WatchTarget](<https://github.com/real-rm/gowatch/blob/main/watch.go#L119>)

```go
func WatchTarget(opt WatchOptions) (*WatchObject, error)
```

WatchTarget starts watching a MongoDB collection for changes

<a name="WatchObject.End"></a>
### func \(\*WatchObject\) [End](<https://github.com/real-rm/gowatch/blob/main/watch.go#L481>)

```go
func (w *WatchObject) End(err error)
```

End stops the watch operation

<a name="WatchObject.FinishAndUpdateSysdata"></a>
### func \(\*WatchObject\) [FinishAndUpdateSysdata](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L280>)

```go
func (w *WatchObject) FinishAndUpdateSysdata(updateSysData UpdateSysDataFunc) error
```

FinishAndUpdateSysdata finishes the watch operation and updates the sysdata

<a name="WatchObject.Pause"></a>
### func \(\*WatchObject\) [Pause](<https://github.com/real-rm/gowatch/blob/main/watch.go#L717>)

```go
func (w *WatchObject) Pause()
```

Pause pauses the watch operation

<a name="WatchObject.ProcessChanges"></a>
### func \(\*WatchObject\) [ProcessChanges](<https://github.com/real-rm/gowatch/blob/main/watch.go#L336>)

```go
func (w *WatchObject) ProcessChanges(opt WatchOptions)
```

ProcessChanges processes changes from the change stream

<a name="WatchObject.Resume"></a>
### func \(\*WatchObject\) [Resume](<https://github.com/real-rm/gowatch/blob/main/watch.go#L722>)

```go
func (w *WatchObject) Resume()
```

Resume resumes the watch operation

<a name="WatchOptions"></a>
## type [WatchOptions](<https://github.com/real-rm/gowatch/blob/main/watch.go#L60-L76>)

WatchOptions defines options for watching MongoDB collections

```go
type WatchOptions struct {
    ImportWatchedRecordsCol *gomongo.MongoCollection                     // Collection to import watched records
    WatchedColl             *gomongo.MongoCollection                     // Collection to watch for changes
    OnChange                func(bson.M, *gomongo.MongoCollection) error // Callback for document changes
    OnError                 func(error)                                  // Callback for error handling
    OnTokenUpdate           func(TokenUpdateOptions) error               // Callback for token updates
    QueryWhenInvalidToken   bson.M                                       // Query to use when token is invalid
    WatchPipeline           []bson.M                                     // MongoDB aggregation pipeline for change stream
    HighWaterMark           int                                          // Maximum documents to process before pausing
    WaterMarkerTimerS       float64                                      // Timer for water mark checks
    UpdateTokenTimerS       float64                                      // Timer for token updates
    SavedToken              *bson.M                                      // Token to resume from
    UpdateProcessStatusFn   func() error                                 // Function to update process status
    OnClose                 func()                                       // Callback when watch is closed
    Context                 context.Context                              // Context for controlling watch lifecycle
    Cancel                  context.CancelFunc                           // Cancel function for the context
}
```

<a name="WatchState"></a>
## type [WatchState](<https://github.com/real-rm/gowatch/blob/main/watch.go#L51>)

WatchState represents the current state of the watch operation

```go
type WatchState int32
```

<a name="WatchStateRunning"></a>

```go
const (
    WatchStateRunning WatchState = iota
    WatchStatePaused
    WatchStateStopped
)
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
