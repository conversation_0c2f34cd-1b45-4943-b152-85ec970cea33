package gowatch

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	// Error codes for MongoDB change stream operations
	ChangeStreamHistoryLost = 286 // Error when change stream history is lost
	InvalidToken            = 280 // Error when token is invalid
	CappedPositionLost      = 136 // Error when capped collection position is lost

	// Default configuration values
	DefaultHighWaterMark     = 10  // Maximum number of documents to process before pausing
	DefaultWaterMarkerTimerS = 600 // 10 minutes timer for water mark checks
	DefaultUpdateTokenTimerS = 60  // 1 minute timer for token updates
	DefaultChangeChanSize    = 100 // Default size for change channel buffer
	DefaultErrorChanSize     = 10  // Default size for error channel buffer

	// Time constants
	DefaultMaxAwaitTime           = 3 * time.Hour          // Maximum await time for change stream
	DefaultSleepInterval          = 100 * time.Millisecond // Default sleep interval for loops
	DefaultHighWaterMarkThreshold = 1                      // Threshold for high water mark warning
	DefaultStateRunning           = 0                      // State value for running
	DefaultStatePaused            = 1                      // State value for paused
	DefaultStateStopped           = 2                      // State value for stopped

	// Atomic operation constants
	DefaultDecrementValue = -1 // Default value for decrementing counters

	// Increment constants
	DefaultIncrementValue = 1 // Default value for incrementing counters

	// Unix timestamp constants
	DefaultUnixTimestamp = 0 // Default Unix timestamp value
)

// WatchState represents the current state of the watch operation
type WatchState int32

const (
	WatchStateRunning WatchState = iota
	WatchStatePaused
	WatchStateStopped
)

// WatchOptions defines options for watching MongoDB collections
type WatchOptions struct {
	ImportWatchedRecordsCol *gomongo.MongoCollection                     // Collection to import watched records
	WatchedColl             *gomongo.MongoCollection                     // Collection to watch for changes
	OnChange                func(bson.M, *gomongo.MongoCollection) error // Callback for document changes
	OnError                 func(error)                                  // Callback for error handling
	OnTokenUpdate           func(TokenUpdateOptions) error               // Callback for token updates
	QueryWhenInvalidToken   bson.M                                       // Query to use when token is invalid
	WatchPipeline           []bson.M                                     // MongoDB aggregation pipeline for change stream
	HighWaterMark           int                                          // Maximum documents to process before pausing
	WaterMarkerTimerS       float64                                      // Timer for water mark checks
	UpdateTokenTimerS       float64                                      // Timer for token updates
	SavedToken              *bson.M                                      // Token to resume from
	UpdateProcessStatusFn   func() error                                 // Function to update process status
	OnClose                 func()                                       // Callback when watch is closed
	Context                 context.Context                              // Context for controlling watch lifecycle
	Cancel                  context.CancelFunc                           // Cancel function for the context
}

// WatchObject represents the watch operation state
type WatchObject struct {
	ctx                   context.Context
	cancel                context.CancelFunc
	changeStream          *mongo.ChangeStream
	currentToken          *bson.M
	savedToken            *bson.M
	savedTokenClusterTs   time.Time
	processingCounter     int32
	processedCounter      int32
	ns                    string
	state                 int32 // atomic state
	toWarnHighWaterLevel  int32 // Changed from bool to int32
	resumeMt              time.Time
	currentTokenClusterTs time.Time
	currentTokenChangeTs  time.Time
	changeChan            chan bson.M
	errorChan             chan error
	doneChan              chan struct{}
	waterMarkerTimer      *gohelper.Interval
	updateTokenTimer      *gohelper.Interval
	watchChangeTimer      *gohelper.Interval
	processChan           chan struct{}
	wg                    sync.WaitGroup
	onClose               func()
	tokenMutex            sync.RWMutex // Add mutex for token synchronization
	isInitialSetup        bool         // Flag to track if this is initial setup
	cleanupOnce           sync.Once    // Ensure cleanup happens only once
}

// setState safely updates the watch state
func (w *WatchObject) setState(newState WatchState) {
	atomic.StoreInt32(&w.state, int32(newState))
}

// getState safely gets the current watch state
func (w *WatchObject) getState() WatchState {
	return WatchState(atomic.LoadInt32(&w.state))
}

// WatchTarget starts watching a MongoDB collection for changes
func WatchTarget(opt WatchOptions) (*WatchObject, error) {
	if opt.WatchedColl == nil {
		return nil, fmt.Errorf("watchedColl required")
	}
	if opt.OnError == nil {
		return nil, fmt.Errorf("onError function required")
	}
	if opt.OnChange == nil {
		return nil, fmt.Errorf("OnChange callback required")
	}
	if opt.Context == nil {
		return nil, fmt.Errorf("context required")
	}
	if opt.Cancel == nil {
		return nil, fmt.Errorf("cancel function required")
	}

	// Set default values
	if opt.HighWaterMark <= 0 {
		opt.HighWaterMark = DefaultHighWaterMark
	}
	if opt.WaterMarkerTimerS == 0 {
		opt.WaterMarkerTimerS = DefaultWaterMarkerTimerS
	}
	if opt.UpdateTokenTimerS == 0 {
		opt.UpdateTokenTimerS = DefaultUpdateTokenTimerS
	}
	if opt.WatchPipeline == nil {
		opt.WatchPipeline = []bson.M{}
	}

	// Create watch object with proper context
	watchObj := &WatchObject{
		ctx:            opt.Context,
		cancel:         opt.Cancel,
		ns:             opt.WatchedColl.Name(),
		state:          int32(WatchStateRunning),
		changeChan:     make(chan bson.M, DefaultChangeChanSize),
		errorChan:      make(chan error, DefaultErrorChanSize),
		doneChan:       make(chan struct{}),
		processChan:    make(chan struct{}, opt.HighWaterMark),
		onClose:        opt.OnClose,
		isInitialSetup: true,
	}

	// Configure change stream options
	opts := options.ChangeStream().SetFullDocument(options.UpdateLookup)
	opts.SetMaxAwaitTime(DefaultMaxAwaitTime)
	if opt.SavedToken != nil {
		opts.SetStartAfter(opt.SavedToken)
	}

	// Create change stream with proper context
	changeStream, err := opt.WatchedColl.Watch(opt.Context, opt.WatchPipeline, opts)
	if err != nil || changeStream == nil {
		watchObj.cleanup()
		return nil, fmt.Errorf("failed to create change stream: %w", err)
	}

	// Check if change stream is in error state
	if err := changeStream.Err(); err != nil {
		watchObj.cleanup()
		return nil, fmt.Errorf("change stream error: %w", err)
	}

	watchObj.changeStream = changeStream
	watchObj.currentToken = opt.SavedToken
	watchObj.savedToken = opt.SavedToken

	// Start timers
	watchObj.startTimers(opt)

	// Start watching in a goroutine
	watchObj.wg.Add(DefaultIncrementValue)
	// Capture isInitialSetup state before starting goroutine
	isInitial := watchObj.isInitialSetup
	go func() {
		defer func() {
			if r := recover(); r != nil {
				golog.Error("watchLoop panic recovered", "error", r)
			}
			watchObj.wg.Done()
			// Only perform cleanup if this is initial setup and goroutine exits normally
			if isInitial {
				watchObj.cleanupOnce.Do(watchObj.finalCleanup)
			}
		}()
		watchObj.watchLoop(opt)
	}()

	// Start processing changes in a goroutine
	watchObj.wg.Add(DefaultIncrementValue)
	// Use the same isInitial value for consistency
	go func() {
		defer func() {
			if r := recover(); r != nil {
				golog.Error("ProcessChanges panic recovered", "error", r)
			}
			watchObj.wg.Done()
			// Only perform cleanup if this is initial setup and goroutine exits normally
			if isInitial {
				watchObj.cleanupOnce.Do(watchObj.finalCleanup)
			}
		}()
		watchObj.ProcessChanges(opt)
	}()

	watchObj.isInitialSetup = false
	return watchObj, nil
}

// watchLoop handles the main watch loop
func (w *WatchObject) watchLoop(opt WatchOptions) {
	defer func() {
		if r := recover(); r != nil {
			golog.Error("watchLoop panic recovered", "error", r)
		}
		// Only close doneChan if it hasn't been closed yet
		select {
		case <-w.doneChan:
			// Channel already closed, do nothing
		default:
			close(w.doneChan)
		}
	}()

	var cursorInUseLock bool
	unlock := func() {
		cursorInUseLock = false
	}

	for {
		// Check if context is done
		select {
		case <-w.ctx.Done():
			return
		default:
		}

		if cursorInUseLock {
			time.Sleep(DefaultSleepInterval)
			continue
		}

		if w.getState() == WatchStatePaused {
			time.Sleep(DefaultSleepInterval)
			continue
		}

		if w.changeStream == nil {
			golog.Error("change stream is nil, stopping watch loop")
			return
		}

		// Check if change stream is in error state
		if err := w.changeStream.Err(); err != nil {
			// Only log error if context is not cancelled
			if w.ctx.Err() == nil {
				golog.Error("change stream error", "error", err)
				w.handleError(opt, err)
			}
			return
		}

		cursorInUseLock = true
		// Use the watch object's context
		hasNext := w.changeStream.Next(w.ctx)
		if !hasNext {
			if err := w.changeStream.Err(); err != nil {
				// Only log error if not cancelled
				if w.getState() != WatchStateStopped && w.ctx.Err() == nil {
					golog.Error("watchLoop", "error", err, "isWatchEnd", w.getState() == WatchStateStopped)
					w.handleError(opt, err)
				}
				unlock()
				return
			}
			// No error and no next, just continue
			unlock()
			continue
		}

		var changedObj bson.M
		if err := w.changeStream.Decode(&changedObj); err != nil {
			// Only log error if not cancelled
			if w.getState() != WatchStateStopped && w.ctx.Err() == nil {
				golog.Error("watchLoop", "error", err, "isWatchEnd", w.getState() == WatchStateStopped)
				w.handleError(opt, err)
			}
			unlock()
			return
		}

		// Update current token with the latest resume token
		if resumeToken := w.changeStream.ResumeToken(); resumeToken != nil {
			var tokenDoc bson.M
			if err := bson.Unmarshal(resumeToken, &tokenDoc); err == nil {
				w.tokenMutex.Lock()
				w.currentToken = &bson.M{"_data": tokenDoc["_data"]}
				w.currentTokenChangeTs = time.Now()
				w.tokenMutex.Unlock()
			}
		}

		unlock()

		// Block until change can be sent to channel
		select {
		case <-w.ctx.Done():
			return
		case w.changeChan <- changedObj:
			// Successfully sent to channel
		}
	}
}

// ProcessChanges processes changes from the change stream
func (w *WatchObject) ProcessChanges(opt WatchOptions) {
	for {
		select {
		case <-w.ctx.Done():
			return
		case err := <-w.errorChan:
			if opt.OnError != nil {
				opt.OnError(err)
			}
			continue
		case changedObj := <-w.changeChan:
			if err := w.processChange(opt, changedObj); err != nil {
				if opt.OnError != nil {
					golog.Error("processChange", "error", err)
					opt.OnError(err)
				}
				continue
			}
		}
	}
}

// processChange handles a single change event
func (w *WatchObject) processChange(opt WatchOptions, changedObj bson.M) error {
	if changedObj == nil {
		return nil
	}

	// Save to import collection if specified
	if opt.ImportWatchedRecordsCol != nil {
		if err := saveChangedObj(opt.ImportWatchedRecordsCol, changedObj); err != nil {
			golog.Error("saveChangedObj", err)
		}
	}

	atomic.AddInt32(&w.processingCounter, DefaultIncrementValue)
	golog.Debug("stream change event:", changedObj["operationType"],
		changedObj["documentKey"], "state", w.getState(),
		"processingCounter", w.processingCounter)

	// Check high water mark using processChan
	select {
	case <-w.ctx.Done():
		return w.ctx.Err()
	case w.processChan <- struct{}{}:
		// Channel not full, continue processing
	}

	err := opt.OnChange(changedObj, opt.WatchedColl)
	atomic.AddInt32(&w.processingCounter, DefaultDecrementValue)
	<-w.processChan
	if err != nil {
		return fmt.Errorf("error processing change: %w", err)
	}

	atomic.AddInt32(&w.processedCounter, DefaultIncrementValue)
	w.resumeMt = time.Now()

	// Reset warning flag when counter drops below high water mark
	if w.processingCounter <= int32(opt.HighWaterMark) {
		atomic.StoreInt32(&w.toWarnHighWaterLevel, DefaultStateRunning)
	}

	return nil
}

// startTimers initializes and starts all required timers
func (w *WatchObject) startTimers(opt WatchOptions) {
	if w.waterMarkerTimer != nil {
		w.waterMarkerTimer.Stop()
	}
	if w.updateTokenTimer != nil {
		w.updateTokenTimer.Stop()
	}
	// Water marker timer - checks processing status periodically
	w.waterMarkerTimer = gohelper.StartInterval(opt.WaterMarkerTimerS, func() {
		if w.getState() == WatchStatePaused {
			return
		}
		select {
		case <-w.ctx.Done():
			return
		default:
			if atomic.LoadInt32(&w.toWarnHighWaterLevel) == DefaultHighWaterMarkThreshold {
				// second time warn
				golog.Warn("high water mark", "HighWaterMark", opt.HighWaterMark, "processingCounter", w.processingCounter)
			} else {
				// first time set warn
				atomic.StoreInt32(&w.toWarnHighWaterLevel, DefaultHighWaterMarkThreshold)
			}
		}
	})

	// Update token timer - updates token periodically
	w.updateTokenTimer = gohelper.StartInterval(opt.UpdateTokenTimerS, func() {
		select {
		case <-w.ctx.Done():
			return
		default:
			if w.getState() == WatchStateRunning {
				w.updateToken(opt)
			}
		}
	})
}

// cleanup handles cleanup operations
func (w *WatchObject) cleanup() {
	// Close change stream if it exists
	if w.changeStream != nil {
		if err := w.changeStream.Close(w.ctx); err != nil {
			golog.Error("error closing change stream", "error", err)
		}
		w.changeStream = nil
	}

	// Stop all timers
	if w.waterMarkerTimer != nil {
		w.waterMarkerTimer.Stop()
		w.waterMarkerTimer = nil
	}
	if w.updateTokenTimer != nil {
		w.updateTokenTimer.Stop()
		w.updateTokenTimer = nil
	}
	if w.watchChangeTimer != nil {
		w.watchChangeTimer.Stop()
		w.watchChangeTimer = nil
	}

	if w.onClose != nil {
		w.onClose()
	}
}

// finalCleanup handles final cleanup when watch is ended
func (w *WatchObject) finalCleanup() {
	// First cancel the context to stop all operations
	if w.cancel != nil {
		w.cancel()
	}
	w.cleanup()
}

// End stops the watch operation
func (w *WatchObject) End(err error) {
	golog.Info("watchObject End", "err", err)
	w.setState(WatchStateStopped)
	if err != nil {
		golog.Error("watchObject End", "error", err)
	}
	w.finalCleanup()
}

// updateToken updates the current token
func (w *WatchObject) updateToken(opt WatchOptions) {
	w.tokenMutex.RLock()
	if w.currentToken == nil {
		w.tokenMutex.RUnlock()
		golog.Debug("_updateTokenTimer", "no token yet")
		return
	}
	currentTokenData := (*w.currentToken)["_data"]
	w.tokenMutex.RUnlock()

	if currentTokenData == nil {
		golog.Debug("_updateTokenTimer", "no token data")
		return
	}

	golog.Debug("_updateTokenTimer", currentTokenData)

	// Update process status if function exists
	if opt.UpdateProcessStatusFn != nil {
		if err := opt.UpdateProcessStatusFn(); err != nil {
			golog.Error("updateProcessStatus", err)
		}
	}

	// Check if token needs update
	w.tokenMutex.RLock()
	currentToken := w.currentToken
	savedToken := w.savedToken
	tokenClusterTs := w.currentTokenClusterTs
	tokenChangeTs := w.currentTokenChangeTs
	resumeMt := w.resumeMt
	w.tokenMutex.RUnlock()

	if currentToken == nil || (*currentToken)["_data"] == nil {
		golog.Debug("no retObject.resumeToken?._data, do not need update", w.ns)
		return
	}

	if savedToken != nil && (*savedToken)["_data"] == (*currentToken)["_data"] && resumeMt.IsZero() {
		golog.Debug("no tokenChange and no resumeMt, do not need update", w.ns)
		return
	}

	golog.Debug("[updateToken in timer] token Changed", w.ns,
		"tokenClusterTs", tokenClusterTs,
		"tokenChangeTs", tokenChangeTs,
		"resumeMt", resumeMt)

	// Update token if OnTokenUpdate exists
	if opt.OnTokenUpdate != nil {
		tokenOpt := TokenUpdateOptions{
			Token:          currentToken,
			TokenClusterTs: tokenClusterTs,
			TokenChangeTs:  tokenChangeTs,
			End:            w.End,
			ResumeMt:       resumeMt,
		}

		err := opt.OnTokenUpdate(tokenOpt)
		if err != nil {
			golog.Error("onTokenUpdate", "error", err)
			opt.OnError(err)
			return
		}

		w.tokenMutex.Lock()
		w.savedToken = currentToken
		w.savedTokenClusterTs = tokenClusterTs
		w.resumeMt = time.Time{} // Clear resumeMt after successful update
		w.tokenMutex.Unlock()

		golog.Debug("[updateToken in timer] token Changed,", w.ns,
			"savedToken", w.savedToken,
			"currentToken", currentToken)
	}
}

// handleError handles watch errors
func (w *WatchObject) handleError(opt WatchOptions, err error) {
	golog.Error("watch error:", "error", err)
	if mongoErr, ok := err.(mongo.CommandError); ok {
		switch mongoErr.Code {
		case InvalidToken, ChangeStreamHistoryLost:
			w.handleInvalidToken(opt)
		case CappedPositionLost:
			w.handleCappedPositionLost(opt)
		default:
			w.cleanup()
			if opt.OnError != nil {
				opt.OnError(err)
			}
		}
	} else {
		golog.Error("mongo error code parse error:", "error", err)
		w.cleanup()
		if opt.OnError != nil {
			opt.OnError(err)
		}
	}
}

// handleInvalidToken handles invalid token events
func (w *WatchObject) handleInvalidToken(opt WatchOptions) {
	golog.Info("on invalid token: queryWhenInvalidToken:", opt.QueryWhenInvalidToken)
	w.getTokenAndProcessQuery(opt)
}

// handleCappedPositionLost handles capped position lost errors
func (w *WatchObject) handleCappedPositionLost(opt WatchOptions) {
	golog.Warn("queryWhenInvalidToken:", opt.QueryWhenInvalidToken)
	golog.Warn("on collection scan died error process")
	w.getTokenAndProcessQuery(opt)
}

// getTokenAndProcessQuery gets a new token and processes remaining documents
func (w *WatchObject) getTokenAndProcessQuery(opt WatchOptions) {
	var query bson.M
	if !w.savedTokenClusterTs.IsZero() {
		query = bson.M{"_mt": bson.M{"$gte": w.savedTokenClusterTs}}
	} else if opt.QueryWhenInvalidToken != nil {
		query = opt.QueryWhenInvalidToken
	} else {
		if opt.OnError != nil {
			opt.OnError(fmt.Errorf("no invalid query"))
		}
		return
	}

	golog.Info("getTokenAndProcessQuery, do query:", query)
	processOneFn := func(item bson.M) error {
		return opt.OnChange(bson.M{
			"operationType": "update",
			"fullDocument":  item,
			"documentKey":   bson.M{"_id": item["_id"]},
		}, opt.WatchedColl)
	}

	queryCtx, queryCancel := context.WithCancel(w.ctx)
	defer queryCancel() // make sure to cancel the context
	token, tokenClusterTs, err := GetNewTokenAndProcessRemainingDocs(GetNewTokenAndProcessRemainingDocsOptions{
		FromCol:       opt.WatchedColl,
		Query:         query,
		ProcessOneFn:  processOneFn,
		HighWaterMark: DefaultHighWaterMarkThreshold,
		Context:       queryCtx,
		Cancel:        queryCancel,
	})

	if err != nil {
		golog.Error("error in getTokenAndProcessQuery:", err)
		if opt.OnError != nil {
			opt.OnError(err)
		}
		return
	}

	if opt.OnTokenUpdate != nil {
		err = opt.OnTokenUpdate(TokenUpdateOptions{
			Token:          token,
			TokenClusterTs: tokenClusterTs,
			TokenChangeTs:  time.Now(),
			End:            w.End,
		})
		if err != nil {
			golog.Error("error in onTokenUpdate:", err)
			if opt.OnError != nil {
				opt.OnError(err)
			}
			return
		}
	}

	golog.Info("start watch after getTokenAndProcessQuery")

	// Create new change stream with the token
	opts := options.ChangeStream().SetFullDocument(options.UpdateLookup)
	if token != nil {
		opts.SetStartAfter(token)
	}

	// Close old change stream if it exists
	if w.changeStream != nil {
		golog.Info("closing old change stream before starting new one")
		closeCtx, closeCancel := context.WithTimeout(w.ctx, 5*time.Second)
		if err := w.changeStream.Close(closeCtx); err != nil {
			if err != context.DeadlineExceeded {
				golog.Error("error closing old change stream", "error", err)
			} else {
				golog.Warn("timeout closing old change stream")
			}
		}
		closeCancel()
		w.changeStream = nil
	}

	// Create new change stream
	changeStream, err := opt.WatchedColl.Watch(w.ctx, []bson.M{}, opts)
	if err != nil {
		golog.Error("error creating new change stream:", err)
		if opt.OnError != nil {
			opt.OnError(err)
		}
		return
	}

	// Create new channels for the new watch loop
	w.tokenMutex.Lock()
	w.changeStream = changeStream
	w.currentToken = token
	w.savedToken = token
	w.currentTokenClusterTs = tokenClusterTs
	w.tokenMutex.Unlock()

	// Reset channels
	w.changeChan = make(chan bson.M, DefaultChangeChanSize)
	w.errorChan = make(chan error, DefaultErrorChanSize)
	w.doneChan = make(chan struct{})

	if w.waterMarkerTimer == nil {
		w.startTimers(opt)
	}
	golog.Info("starting new watchLoop after old one is closed")
	go w.watchLoop(opt)
}

// Pause pauses the watch operation
func (w *WatchObject) Pause() {
	w.setState(WatchStatePaused)
}

// Resume resumes the watch operation
func (w *WatchObject) Resume() {
	w.setState(WatchStateRunning)
}

// updateTokenFromChangeEvent updates the token based on change event
func (w *WatchObject) updateTokenFromChangeEvent(changeObj bson.M) {
	if w == nil {
		golog.Error("watchObject is nil, cannot update token")
		return
	}
	if changeObj == nil || changeObj["_id"] == nil || changeObj["clusterTime"] == nil {
		return
	}

	w.tokenMutex.Lock()

	if id, ok := changeObj["_id"].(bson.M); ok {
		w.currentToken = &id
	}
	if clusterTime, ok := changeObj["clusterTime"].(primitive.Timestamp); ok {
		w.currentTokenClusterTs = time.Unix(int64(clusterTime.T), DefaultUnixTimestamp)
	}
	w.currentTokenChangeTs = time.Now()
	w.tokenMutex.Unlock()

	golog.Debug("update currentToken after finished process changed event",
		"currentToken", w.currentToken,
		"currentTokenClusterTs", w.currentTokenClusterTs,
		"savedToken", w.savedToken)
}
