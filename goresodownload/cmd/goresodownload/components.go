package main

import (
	"fmt"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goresodownload"
)

// isValidBoard checks if the provided board type is valid
func isValidBoard(board string) bool {
	_, exists := goresodownload.BoardMergedTable[board]
	return exists
}

// createComponents creates and returns the analyzer, downloader, dirStore, and queue components
func createComponents() error {
	golog.Info("Creating components", "boardType", gBoardType)

	// Create analyzer
	golog.Debug("Creating analyzer")
	analyzer = goresodownload.NewMediaDiffAnalyzer()

	// Create downloader
	golog.Debug("Creating downloader")
	var err error
	downloader, err = createDownloader()
	if err != nil {
		golog.Error("Failed to create downloader", "error", err)
		return fmt.Errorf("failed to create downloader: %w", err)
	}
	golog.Debug("Downloader created successfully")

	// Create dirStore
	golog.Debug("Creating dirStore")
	collection := gomongo.Coll("rni", "file_server")
	dirStore, err = levelStore.NewDirKeyStore(gBoardType, collection, "")
	if err != nil {
		golog.Error("Failed to create dirKeyStore", "error", err)
		return fmt.Errorf("failed to create dirKeyStore: %w", err)
	}
	if dirStore == nil {
		golog.Error("dirKeyStore is nil")
		return fmt.Errorf("failed to create dirKeyStore")
	}
	golog.Debug("DirStore created successfully")

	// Create download queue
	golog.Debug("Creating download queue")
	downloadQueue, err = goresodownload.NewResourceDownloadQueue(QueueCol)
	if err != nil {
		golog.Error("Failed to create download queue", "error", err)
		return fmt.Errorf("failed to create download queue: %w", err)
	}
	golog.Debug("Download queue created successfully")

	golog.Info("All components created successfully")
	return nil
}

// createDownloader creates a new downloader instance
func createDownloader() (*goresodownload.Downloader, error) {
	// Get image directories from config
	storagePaths, err := levelStore.GetImageDir(gBoardType)
	if err != nil {
		return nil, fmt.Errorf("failed to get image directories: %w", err)
	}
	if len(storagePaths) == 0 {
		return nil, fmt.Errorf("no image directories configured for board %s", gBoardType)
	}

	// Create downloader options
	opts := &goresodownload.DownloaderOptions{
		Config:       goresodownload.NewDefaultConfig(),
		StoragePaths: storagePaths,
		MergedCol:    gomongo.Coll("rni", goresodownload.BoardMergedTable[gBoardType]),
		FailedCol:    FailedCol,
		SpeedMeter:   speedMeter,
	}

	return goresodownload.NewDownloader(opts)
}
